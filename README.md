# 囤鼠科技官网 (TunshuTech Website)

一个基于 Vue 3 + TypeScript + Tailwind CSS 构建的现代化企业官网，展示囤鼠科技的AI产品和解决方案。

## 🚀 项目特性

- **现代化技术栈**: Vue 3 Composition API + TypeScript + Vite
- **响应式设计**: 基于 Tailwind CSS 的移动端优先设计
- **组件化开发**: 可复用的 Vue 组件架构
- **路由管理**: Vue Router 4 实现的单页应用
- **图标系统**: Heroicons 提供的精美图标
- **动画效果**: 流畅的页面过渡和交互动画
- **SEO友好**: 语义化HTML结构

## 📋 环境要求

在开始之前，请确保您的开发环境满足以下要求：

- **Node.js**: >= 16.0.0 (推荐使用 18.x 或更高版本)
- **npm**: >= 8.0.0 或 **yarn**: >= 1.22.0 或 **pnpm**: >= 7.0.0
- **Git**: 用于版本控制

### 检查环境版本
```bash
node --version
npm --version
```

## 🛠️ 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-username/TunshuTech_Website.git
cd TunshuTech_Website
```

### 2. 安装依赖
```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

### 3. 启动开发服务器
```bash
# 使用 npm
npm run dev

# 或使用 yarn
yarn dev

# 或使用 pnpm
pnpm dev
```

启动成功后，在浏览器中访问 `http://localhost:5173` 查看网站。

### 4. 构建生产版本
```bash
# 使用 npm
npm run build

# 或使用 yarn
yarn build

# 或使用 pnpm
pnpm build
```

### 5. 预览生产版本
```bash
# 使用 npm
npm run preview

# 或使用 yarn
yarn preview

# 或使用 pnpm
pnpm preview
```

## 📁 项目结构

```
TunshuTech_Website/
├── public/                 # 静态资源目录
│   ├── images/            # 图片资源
│   │   └── hero-bg.jpg    # 首页背景图
│   └── logo/              # Logo文件
│       └── logo.svg       # 公司Logo (SVG格式)
├── src/                   # 源代码目录
│   ├── assets/            # 项目资源
│   │   ├── logo.svg       # SVG Logo
│   │   └── main.css       # 全局样式
│   ├── components/        # 可复用组件
│   │   ├── Footer.vue     # 页脚组件
│   │   ├── NavBar.vue     # 导航栏组件
│   │   ├── PageHeader.vue # 页面头部组件
│   │   └── ProductHeader.vue # 产品页头部组件
│   ├── router/            # 路由配置
│   │   └── index.ts       # 路由定义
│   ├── views/             # 页面组件
│   │   ├── HomeView.vue   # 首页
│   │   ├── about/         # 关于我们页面
│   │   │   ├── CompanyView.vue  # 公司介绍
│   │   │   └── CareersView.vue  # 招聘信息
│   │   ├── products/      # 产品页面
│   │   │   └── EduPlatformView.vue # 教育平台
│   │   └── solutions/     # 解决方案页面
│   │       └── StudyAbroadView.vue # 留学咨询
│   ├── App.vue            # 根组件
│   ├── main.ts            # 应用入口
│   └── env.d.ts           # TypeScript环境声明
├── index.html             # HTML模板
├── package.json           # 项目配置和依赖
├── tailwind.config.js     # Tailwind CSS配置
├── tsconfig.json          # TypeScript配置
├── vite.config.ts         # Vite构建配置
└── README.md              # 项目说明文档
```

## 🎨 技术栈详解

### 前端框架
- **Vue 3**: 采用 Composition API，提供更好的TypeScript支持和代码组织
- **TypeScript**: 提供类型安全和更好的开发体验
- **Vite**: 快速的构建工具，支持热模块替换(HMR)

### 样式方案
- **Tailwind CSS**: 实用优先的CSS框架，快速构建现代化界面
- **PostCSS**: CSS后处理器，支持现代CSS特性

### 路由和状态
- **Vue Router 4**: 官方路由管理器，支持嵌套路由和路由守卫

### 图标和UI
- **Heroicons**: 精美的SVG图标库
- **响应式设计**: 移动端优先的设计理念

## 🔧 开发指南

### 添加新页面
1. 在 `src/views/` 目录下创建新的Vue组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 在导航栏中添加对应的链接

### 自定义样式
项目使用 Tailwind CSS，推荐使用其实用类进行样式开发。如需自定义样式：
1. 在 `tailwind.config.js` 中扩展主题配置
2. 在组件的 `<style>` 标签中添加自定义CSS

### 组件开发规范
- 使用 Composition API 和 `<script setup>` 语法
- 为组件添加 TypeScript 类型定义
- 遵循 Vue 3 的最佳实践

## 🌐 页面功能说明

### 首页 (/)
- **Hero区域**: 展示公司核心理念和价值主张
- **行业应用**: 展示AI技术在各行业的应用场景
- **联系表单**: 客户咨询和预约功能

### 产品页面 (/products)
- **TunShuEdu AI智教平台** (`/products/edu-platform`): 展示教育平台的功能特性

### 解决方案 (/solutions)
- **留学咨询解决方案** (`/solutions/study-abroad`): 展示留学服务的AI应用

### 关于我们 (/about)
- **公司介绍** (`/about/company`): 公司历史、愿景和团队介绍
- **招聘信息** (`/about/careers`): 职位发布和招聘流程

## 🎯 自定义配置

### 主题色彩
在 `tailwind.config.js` 中可以自定义主题色彩：
```javascript
theme: {
  extend: {
    colors: {
      primary: {
        // 自定义主色调
        500: '#0ea5e9',
        600: '#0284c7',
        // ...
      }
    }
  }
}
```

### 联系信息
在 `src/views/HomeView.vue` 中更新联系信息：
```javascript
const contactInfo = [
  {
    name: '地址',
    value: '您的公司地址',
    icon: BuildingOfficeIcon
  },
  // ...
]
```

## 🚀 部署指南

### 静态部署
1. 构建项目：`npm run build`
2. 将 `dist/` 目录部署到静态服务器

### Vercel部署
1. 连接GitHub仓库到Vercel
2. 设置构建命令：`npm run build`
3. 设置输出目录：`dist`

### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 提交 Pull Request

## 📝 更新日志

### v0.0.1 (当前版本)
- ✅ 初始项目搭建
- ✅ 首页和主要页面开发
- ✅ 响应式设计实现
- ✅ 路由和导航配置

## 📞 联系方式

- **公司**: 囤鼠科技
- **地址**: 成都市锦江区华宇广场B座
- **电话**: 18180992055
- **邮箱**: <EMAIL>

## 📱 当前可访问的页面

- **首页**: http://localhost:5173/
- **TunShuEdu AI智教平台**: http://localhost:5173/products/edu-platform
- **留学咨询解决方案**: http://localhost:5173/solutions/study-abroad
- **关于我们**: http://localhost:5173/about/company
- **加入我们**: http://localhost:5173/about/careers

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**注意**: 这是一个企业官网项目，请确保在生产环境中正确配置所有必要的安全设置和性能优化。
