import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import AIServerView from '../views/products/AIServerView.vue'
import EduPlatformView from '../views/products/EduPlatformView.vue'
import CompanyView from '../views/about/CompanyView.vue'
import CareersView from '../views/about/CareersView.vue'
import StudyAbroadView from '../views/solutions/StudyAbroadView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior() {
    // 始终滚动到顶部
    return { top: 0 }
  },
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView
    },
    {
      path: '/products/ai-server',
      name: 'aiServer',
      component: AIServerView
    },
    {
      path: '/products/edu-platform',
      name: 'eduPlatform',
      component: EduPlatformView
    },
    {
      path: '/solutions/study-abroad',
      name: 'studyAbroad',
      component: StudyAbroadView
    },
    {
      path: '/about/company',
      name: 'company',
      component: CompanyView
    },
    {
      path: '/about/careers',
      name: 'careers',
      component: CareersView
    }
  ]
})

export default router