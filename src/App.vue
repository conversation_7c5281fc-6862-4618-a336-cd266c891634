<template>
  <div class="min-h-screen bg-gray-50 flex flex-col">
    <nav class="bg-white shadow fixed w-full top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-20">
          <div class="flex-shrink-0 flex items-center">
            <router-link to="/" class="block">
              <img class="h-12 w-auto" src="/logo/logo.png" alt="囤鼠科技" />
            </router-link>
          </div>
          <div class="hidden sm:flex sm:items-center sm:space-x-8">
            <!-- 首页 -->
            <router-link to="/" class="nav-item" exact-active-class="active">
              首页
            </router-link>

            <!-- 产品下拉菜单 -->
            <div class="relative group">
              <div class="nav-item cursor-default" :class="{ 'active': $route.path.startsWith('/products') }">
                囤鼠产品
              </div>
              <div class="dropdown-menu">
                <router-link to="/products/ai-server" class="dropdown-item" active-class="active-dropdown">
                  囤鼠AI大模型一体机
                </router-link>
                <router-link to="/products/edu-platform" class="dropdown-item" active-class="active-dropdown">
                  TunShuEdu AI智教平台
                </router-link>
              </div>
            </div>

            <!-- 解决方案下拉菜单 -->
            <div class="relative group">
              <div class="nav-item cursor-default" :class="{ 'active': $route.path.startsWith('/solutions') }">
                解决方案
              </div>
              <div class="dropdown-menu">
                <router-link to="/solutions/study-abroad" class="dropdown-item" active-class="active-dropdown">
                  留学咨询解决方案
                </router-link>
              </div>
            </div>

            <!-- 关于我们下拉菜单 -->
            <div class="relative group">
              <div class="nav-item cursor-default" :class="{ 'active': $route.path.startsWith('/about') }">
                关于我们
              </div>
              <div class="dropdown-menu">
                <router-link to="/about/company" class="dropdown-item" active-class="active-dropdown">
                  关于我们
                </router-link>
                <router-link to="/about/careers" class="dropdown-item" active-class="active-dropdown">
                  加入我们
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <main class="flex-grow pt-20">
      <router-view />
    </main>

    <Footer />
  </div>
</template>

<script setup lang="ts">
import Footer from './components/Footer.vue'
</script>

<style>
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

.nav-item {
  @apply px-4 py-2 text-gray-900 font-medium text-base inline-flex items-center transition-colors duration-150;
  height: 80px;
  line-height: 80px;
  position: relative;
}

.nav-item:hover,
.group:hover .nav-item {
  @apply text-primary-600;
}

.nav-item.active {
  @apply text-primary-600;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  @apply bg-primary-600;
}

.dropdown-menu {
  @apply absolute left-1/2 mt-0 w-60 bg-white shadow-lg rounded-b-lg py-2 opacity-0 invisible;
  transform: translateX(-50%) translateY(-10px);
  transition: all 0.2s ease-in-out;
  z-index: 50;
}

.group:hover .dropdown-menu {
  @apply opacity-100 visible;
  transform: translateX(-50%) translateY(0);
}

.dropdown-item {
  @apply block px-4 py-3 text-base text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-150;
  white-space: nowrap;
}

.active-dropdown {
  @apply text-primary-600 bg-gray-50;
}

.dropdown-divider {
  @apply my-2 border-t border-gray-100;
}

.dropdown-email {
  @apply px-4 py-3;
}
</style> 