<template>
  <div class="relative bg-[#1C1F23] py-32">
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-br from-primary-600/20 to-primary-900/20"></div>
    </div>
    <div class="relative mx-auto max-w-7xl px-6 lg:px-8">
      <h1 class="text-4xl font-bold tracking-tight text-white sm:text-6xl animate-float-in">
        <slot></slot>
      </h1>
      <p v-if="$slots.subtitle" class="mt-6 text-xl leading-8 text-gray-300 animate-float-in animation-delay-200">
        <slot name="subtitle"></slot>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
// No props needed
</script>

<style scoped>
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

@keyframes floatIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-float-in {
  animation: floatIn 0.8s ease-out forwards;
  opacity: 0;
}

.animation-delay-200 {
  animation-delay: 200ms;
}
</style> 