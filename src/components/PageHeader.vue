<template>
  <div class="relative bg-gray-900 py-32">
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-br from-primary-500 to-purple-600 opacity-20"></div>
      <div class="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [background-size:16px_16px]"></div>
    </div>
    <div class="relative mx-auto max-w-7xl px-6 lg:px-8">
      <h1 class="text-4xl font-bold tracking-tight text-white sm:text-6xl fade-in-up">
        <slot></slot>
      </h1>
      <p v-if="$slots.subtitle" class="mt-6 text-xl leading-8 text-gray-300 fade-in-up" style="animation-delay: 200ms">
        <slot name="subtitle"></slot>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
// No props or additional logic needed
</script>

<style scoped>
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}
</style> 