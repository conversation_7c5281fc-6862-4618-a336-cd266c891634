<template>
  <nav class="bg-white shadow-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="h-16 grid grid-cols-12 items-center">
        <!-- Logo -->
        <div class="col-span-2">
          <router-link to="/" class="text-xl font-bold text-primary-600">囤鼠科技</router-link>
        </div>
        
        <!-- Navigation Links -->
        <div class="col-span-10">
          <div class="grid grid-cols-6 items-center">
            <router-link to="/" class="nav-link">首页</router-link>
            
            <!-- 产品下拉菜单 -->
            <div class="relative">
              <button @click="toggleProductMenu" 
                      @blur="closeProductMenu"
                      class="nav-link w-full">
                囤鼠产品
                <svg class="ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
              
              <!-- 产品菜单 -->
              <div v-show="showProductMenu" 
                   class="absolute top-16 left-0 w-48 py-2 bg-white rounded-md shadow-lg z-10">
                <router-link to="/products/ai-server" 
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                           @click="closeProductMenu">
                  囤鼠AI大模型一体机
                </router-link>
                <router-link to="/products/edu-platform" 
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                           @click="closeProductMenu">
                  TunShuEdu AI智教平台
                </router-link>
              </div>
            </div>
            
            <!-- 解决方案下拉菜单 -->
            <div class="relative">
              <button @click="toggleSolutionMenu" 
                      @blur="closeSolutionMenu"
                      class="nav-link w-full">
                解决方案
                <svg class="ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
              
              <!-- 解决方案菜单 -->
              <div v-show="showSolutionMenu" 
                   class="absolute top-16 left-0 w-48 py-2 bg-white rounded-md shadow-lg z-10">
                <router-link to="/solutions/study-abroad" 
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                           @click="closeSolutionMenu">
                  留学咨询解决方案
                </router-link>
              </div>
            </div>
            
            <!-- 关于我们下拉菜单 -->
            <div class="relative">
              <button @click="toggleAboutMenu" 
                      @blur="closeAboutMenu"
                      class="nav-link w-full">
                关于我们
                <svg class="ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
              
              <!-- 关于我们菜单 -->
              <div v-show="showAboutMenu" 
                   class="absolute top-16 left-0 w-48 py-2 bg-white rounded-md shadow-lg z-10">
                <router-link to="/about/company" 
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                           @click="closeAboutMenu">
                  关于我们
                </router-link>
                <router-link to="/about/careers" 
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                           @click="closeAboutMenu">
                  加入我们
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const showProductMenu = ref(false)
const showSolutionMenu = ref(false)
const showAboutMenu = ref(false)

const toggleProductMenu = () => {
  showProductMenu.value = !showProductMenu.value
  showSolutionMenu.value = false
  showAboutMenu.value = false
}

const closeProductMenu = () => {
  setTimeout(() => {
    showProductMenu.value = false
  }, 200)
}

const toggleSolutionMenu = () => {
  showSolutionMenu.value = !showSolutionMenu.value
  showProductMenu.value = false
  showAboutMenu.value = false
}

const closeSolutionMenu = () => {
  setTimeout(() => {
    showSolutionMenu.value = false
  }, 200)
}

const toggleAboutMenu = () => {
  showAboutMenu.value = !showAboutMenu.value
  showProductMenu.value = false
  showSolutionMenu.value = false
}

const closeAboutMenu = () => {
  setTimeout(() => {
    showAboutMenu.value = false
  }, 200)
}
</script>

<style scoped>
.nav-link {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 4rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  white-space: nowrap;
  transition: color 0.2s;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: transparent;
  transition: background-color 0.2s;
}

.router-link-active.nav-link {
  color: #4F46E5;
}

.router-link-active.nav-link::after {
  background-color: #4F46E5;
}

.router-link-active.block {
  border-bottom: none;
}
</style> 