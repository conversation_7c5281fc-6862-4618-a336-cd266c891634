<template>
  <footer class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-gray-300 relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute top-0 left-0 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"></div>
      <div class="absolute bottom-0 right-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
    </div>

    <div class="relative mx-auto max-w-7xl px-6 py-16 lg:px-8 lg:py-20">
      <!-- 主要内容区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-5 gap-12 mb-16">
        <!-- 公司信息 -->
        <div class="lg:col-span-2 space-y-6">
          <div class="flex items-center space-x-3">
            <img class="h-10 w-auto" src="/logo/logo.svg" alt="囤鼠科技" />
            <span class="text-xl font-bold text-white">囤鼠科技</span>
          </div>
          <p class="text-gray-400 leading-relaxed max-w-md">
            致力于吸收前沿AI科技，深入行业需求场景，搭建核心技术与行业应用的桥梁，提供领先的智能化解决方案。
          </p>
          <div class="flex space-x-4">
            <!-- 社交媒体图标 -->
            <a href="#" class="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors duration-300 group">
              <svg class="w-5 h-5 text-gray-400 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
              </svg>
            </a>
            <a href="#" class="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors duration-300 group">
              <svg class="w-5 h-5 text-gray-400 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
              </svg>
            </a>
            <a href="#" class="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors duration-300 group">
              <svg class="w-5 h-5 text-gray-400 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </a>
          </div>
        </div>

        <!-- 导航链接 -->
        <div class="lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- 囤鼠产品 -->
          <div class="space-y-6">
            <h3 class="text-lg font-semibold text-white flex items-center">
              <div class="w-1 h-6 bg-primary-500 rounded-full mr-3"></div>
              囤鼠产品
            </h3>
            <ul role="list" class="space-y-3">
              <li>
                <router-link to="/products/ai-server"
                           class="text-gray-400 hover:text-primary-400 transition-colors duration-300 flex items-center group"
                           @click="scrollToTop">
                  <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  囤鼠AI大模型一体机
                </router-link>
              </li>
              <li>
                <router-link to="/products/edu-platform"
                           class="text-gray-400 hover:text-primary-400 transition-colors duration-300 flex items-center group"
                           @click="scrollToTop">
                  <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  TunShuEdu AI智教平台
                </router-link>
              </li>
            </ul>
          </div>

          <!-- 解决方案 -->
          <div class="space-y-6">
            <h3 class="text-lg font-semibold text-white flex items-center">
              <div class="w-1 h-6 bg-primary-500 rounded-full mr-3"></div>
              解决方案
            </h3>
            <ul role="list" class="space-y-3">
              <li>
                <router-link to="/solutions/study-abroad"
                           class="text-gray-400 hover:text-primary-400 transition-colors duration-300 flex items-center group"
                           @click="scrollToTop">
                  <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  智能留学教育解决方案
                </router-link>
              </li>
            </ul>
          </div>

          <!-- 关于我们 -->
          <div class="space-y-6">
            <h3 class="text-lg font-semibold text-white flex items-center">
              <div class="w-1 h-6 bg-primary-500 rounded-full mr-3"></div>
              关于我们
            </h3>
            <ul role="list" class="space-y-3">
              <li>
                <router-link to="/about/company"
                           class="text-gray-400 hover:text-primary-400 transition-colors duration-300 flex items-center group"
                           @click="scrollToTop">
                  <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  关于我们
                </router-link>
              </li>
              <li>
                <router-link to="/about/careers"
                           class="text-gray-400 hover:text-primary-400 transition-colors duration-300 flex items-center group"
                           @click="scrollToTop">
                  <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  加入我们
                </router-link>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 联系方式卡片 -->
      <div class="mb-16">
        <div class="bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50">
          <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
            <svg class="w-6 h-6 text-primary-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            联系我们
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-primary-500/20 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </div>
              <div>
                <p class="text-sm text-gray-400">电话</p>
                <p class="text-white font-medium">18180992055</p>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-primary-500/20 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <div>
                <p class="text-sm text-gray-400">邮箱</p>
                <p class="text-white font-medium"><EMAIL></p>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-primary-500/20 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </div>
              <div>
                <p class="text-sm text-gray-400">地址</p>
                <p class="text-white font-medium">成都市锦江区华宇广场B座</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部版权信息 -->
      <div class="border-t border-gray-700/50 pt-8">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <p class="text-sm text-gray-400">
            &copy; 2024 囤鼠科技有限公司 版权所有
          </p>
          <div class="flex space-x-6 text-sm">
            <a href="http://www.beian.gov.cn/portal/registerSystemInfo"
               target="_blank"
               rel="noreferrer"
               class="text-gray-400 hover:text-primary-400 transition-colors duration-300">
              川公网安备51019002007817号
            </a>
            <a href="https://beian.miit.gov.cn/"
               target="_blank"
               class="text-gray-400 hover:text-primary-400 transition-colors duration-300">
              蜀ICP备2025128320号
            </a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'instant'
  })
}
</script>