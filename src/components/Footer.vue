<template>
  <footer class="bg-[#1C1F23] text-gray-400">
    <div class="mx-auto max-w-7xl px-6 py-12 lg:px-8 lg:py-16">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 中间三列导航 -->
        <div class="col-span-3 grid grid-cols-3 gap-8">
          <!-- 囤鼠产品 -->
          <div class="space-y-4">
            <h3 class="text-sm font-semibold text-gray-200">囤鼠产品</h3>
            <ul role="list" class="space-y-2">
              <li>
                <router-link to="/products/ai-server" class="text-sm hover:text-gray-200" @click="scrollToTop">囤鼠AI大模型一体机</router-link>
              </li>
              <li>
                <router-link to="/products/edu-platform" class="text-sm hover:text-gray-200" @click="scrollToTop">TunShuEdu AI智教平台</router-link>
              </li>
            </ul>
          </div>

          <!-- 解决方案 -->
          <div class="space-y-4">
            <h3 class="text-sm font-semibold text-gray-200">解决方案</h3>
            <ul role="list" class="space-y-2">
              <li>
                <router-link to="/solutions/study-abroad" class="text-sm hover:text-gray-200" @click="scrollToTop">智能留学教育解决方案</router-link>
              </li>
            </ul>
          </div>

          <!-- 关于我们 -->
          <div class="space-y-4">
            <h3 class="text-sm font-semibold text-gray-200">关于我们</h3>
            <ul role="list" class="space-y-2">
              <li>
                <router-link to="/about/company" class="text-sm hover:text-gray-200" @click="scrollToTop">关于我们</router-link>
              </li>
              <li>
                <router-link to="/about/careers" class="text-sm hover:text-gray-200" @click="scrollToTop">加入我们</router-link>
              </li>
            </ul>
          </div>
        </div>

        <!-- 联系方式 -->
        <div class="space-y-4">
          <h3 class="text-sm font-semibold text-gray-200">联系方式</h3>
          <ul role="list" class="space-y-2">
            <li class="text-sm">电话：400-888-8888</li>
            <li class="text-sm">邮箱：<EMAIL></li>
            <li class="text-sm">地址：成都市锦江区华宇广场B座</li>
          </ul>
        </div>
      </div>

      <div class="mt-12 border-t border-gray-800 pt-8">
        <p class="text-xs text-gray-400">&copy; 2024 囤鼠科技有限公司 版权所有 | <a href="http://www.beian.gov.cn/portal/registerSystemInfo" target="_blank" rel="noreferrer" class="hover:text-gray-200">川公网安备51019002007817号</a> | <a href="https://beian.miit.gov.cn/" target="_blank" class="hover:text-gray-200">蜀ICP备2025128320号</a></p>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'instant'
  })
}
</script> 