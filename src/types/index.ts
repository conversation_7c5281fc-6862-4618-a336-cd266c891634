// 全局类型定义文件

export interface ContactInfo {
  name: string
  value: string
  icon: any
}

export interface Industry {
  name: string
  description: string
  icon: any
}

export interface CoreService {
  name: string
  description: string
  icon: any
  technologies: string[]
}

export interface FormData {
  name: string
  company: string
  phone: string
  email: string
  message: string
}

export interface Feature {
  name: string
  description: string
  icon: any
}

export interface Milestone {
  year: string
  content: string
  icon: any
}

export interface Value {
  name: string
  description: string
  icon: any
}
