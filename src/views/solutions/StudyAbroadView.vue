<template>
  <div class="min-h-screen bg-gray-50">
    <PageHeader>
      留学咨询解决方案
      <template #subtitle>
        为留学咨询机构提供一站式AI支持，提升服务效率与转化率
      </template>
    </PageHeader>

    <div class="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
      <!-- 方案概述 -->
      <div class="mx-auto max-w-2xl lg:mx-0">
        <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">方案概述</h2>
        <p class="mt-6 text-lg leading-8 text-gray-600">
          解决方案整合了先进的大模型和多模态技术，在各环节为留学咨询行业服务增效，方案包含囤鼠大模型一体机和TunShuEdu AI智教平台的部署，可提供更多客制化功能选择。
        </p>
      </div>

      <!-- 核心功能 -->
      <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <h3 class="text-2xl font-bold tracking-tight text-gray-900 mb-8">核心功能</h3>
        <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
          <div v-for="feature in features" :key="feature.name" class="flex flex-col">
            <dt class="text-base font-semibold leading-7 text-gray-900">
              <div class="mb-6 flex h-10 w-10 items-center justify-center rounded-lg bg-indigo-600">
                <component :is="feature.icon" class="h-6 w-6 text-white" aria-hidden="true" />
              </div>
              {{ feature.name }}
            </dt>
            <dd class="mt-1 flex flex-auto flex-col text-base leading-7 text-gray-600">
              <p class="flex-auto">{{ feature.description }}</p>
            </dd>
          </div>
        </dl>
      </div>

      <!-- 服务流程 -->
      <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <h3 class="text-2xl font-bold tracking-tight text-gray-900 mb-8">服务流程</h3>
        <div class="flow-root">
          <ul role="list" class="-mb-8">
            <li v-for="(step, stepIdx) in steps" :key="step.name">
              <div class="relative pb-8">
                <span v-if="stepIdx !== steps.length - 1" class="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                <div class="relative flex space-x-3">
                  <div>
                    <span class="h-8 w-8 rounded-full bg-indigo-600 flex items-center justify-center ring-8 ring-white">
                      <component :is="step.icon" class="h-5 w-5 text-white" aria-hidden="true" />
                    </span>
                  </div>
                  <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                    <div>
                      <p class="text-sm font-medium text-gray-900">{{ step.name }}</p>
                      <p class="mt-2 text-sm text-gray-500">{{ step.description }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>

      <!-- 方案优势 -->
      <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <h3 class="text-2xl font-bold tracking-tight text-gray-900 mb-8">方案优势</h3>
        <div class="grid grid-cols-1 gap-x-8 gap-y-16 lg:grid-cols-2">
          <div v-for="advantage in advantages" :key="advantage.title" class="flex flex-col bg-white rounded-2xl shadow-lg overflow-hidden">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-indigo-600">
                  <component :is="advantage.icon" class="h-6 w-6 text-white" aria-hidden="true" />
                </div>
                <h3 class="ml-4 text-xl font-semibold text-gray-900">{{ advantage.title }}</h3>
              </div>
              <p class="mt-4 text-gray-600">{{ advantage.description }}</p>
              <ul class="mt-6 space-y-3">
                <li v-for="point in advantage.points" :key="point" class="flex gap-x-3">
                  <CheckCircleIcon class="h-6 w-5 flex-none text-indigo-600" aria-hidden="true" />
                  <span class="text-sm text-gray-600">{{ point }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  UserGroupIcon,
  DocumentTextIcon,
  ChartBarIcon,
  RocketLaunchIcon,
  ChatBubbleLeftRightIcon,
  DocumentCheckIcon,
  ClockIcon,
  CheckCircleIcon,
  ShieldCheckIcon,
} from '@heroicons/vue/24/outline'
import PageHeader from '@/components/PageHeader.vue'

const features = [
  {
    name: '客户管理',
    description: '自动记录整理客户信息资料，监管申请流程进度，日历式讯息提醒及任务分配。',
    icon: UserGroupIcon,
  },
  {
    name: '文案生成助手',
    description: '基于大模型的文书写作助手，协助顾问高效完成文书、简历、推荐信等文案撰写。',
    icon: DocumentTextIcon,
  },
  {
    name: '选校定专推荐',
    description: '根据学生背景信息，深度调用案例及专业信息数据库，匹配冲稳保各级目标专业',
    icon: ChartBarIcon,
  },
]

const steps = [
  {
    name: '需求咨询',
    description: 'AI助手协助顾问快速了解客户需求，智能推荐适合的留学方案。',
    icon: ChatBubbleLeftRightIcon,
  },
  {
    name: '方案制定',
    description: '基于案例及院校数据，为客户定制个性化的留学规划方案。',
    icon: DocumentTextIcon,
  },
  {
    name: '文书准备',
    description: 'AI文书助手协助高效完成各类申请文书的撰写和修改。',
    icon: DocumentCheckIcon,
  },
  {
    name: '申请递交',
    description: '智能化流程管理，确保申请材料完整性和时效性。',
    icon: RocketLaunchIcon,
  }
]

const advantages = [
  {
    title: '效率提升',
    description: '通过AI技术提高咨询服务效率，降低运营成本。',
    icon: ClockIcon,
    points: [
      '智能客户管理系统提高响应速度',
      'AI文书助手加速文书处理效率',
      '自动化流程减少人工操作时间',
      '数据分析辅助快速决策'
    ]
  },
  {
    title: '服务升级',
    description: '提供更专业、更个性化的留学咨询服务。',
    icon: ShieldCheckIcon,
    points: [
      '基于历史案例数据精准方案推荐',
      '个性化的留学规划建议',
      '全流程的智能化服务支持',
      '持续的服务质量监控和优化'
    ]
  }
]
</script>

<style scoped>
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
</style> 