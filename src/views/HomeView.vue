<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Hero Section with Slogan -->
    <div class="relative isolate h-screen overflow-hidden bg-white">
      <!-- 装饰性几何图形 -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-20 left-10 w-32 h-32 bg-primary-500/10 rounded-full blur-xl"></div>
        <div class="absolute bottom-20 right-10 w-48 h-48 bg-blue-500/10 rounded-full blur-xl"></div>
        <div class="absolute top-1/2 left-1/4 w-24 h-24 bg-purple-500/10 rounded-full blur-lg"></div>
      </div>

      <div class="relative h-full w-full flex items-center justify-center">
        <div class="max-w-4xl px-6 text-center">
          <h1 class="text-5xl lg:text-7xl font-bold tracking-tight text-gray-900 opacity-0 fade-in-up">
            <span class="block">AI赋能产业</span>
            <span class="block text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent mt-2">数字化革新</span>
          </h1>
          <p class="mt-8 text-xl lg:text-2xl leading-relaxed text-gray-600 opacity-0 fade-in-up delay-200 max-w-3xl mx-auto">
            囤鼠科技致力于吸收前沿AI科技，深入行业需求场景，搭建核心技术与行业应用的桥梁，提供领先的智能化解决方案，为企业数字变革保驾护航。
          </p>
          <div class="mt-12 flex flex-col sm:flex-row items-center justify-center gap-6 opacity-0 fade-in-up delay-400">
            <a href="#contact" class="btn-primary group">
              立即咨询
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
              </svg>
            </a>
            <router-link to="/about/company" class="btn-outline group">
              了解更多
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
              </svg>
            </router-link>
          </div>


        </div>
      </div>
    </div>

    <!-- Industries Section -->
    <div class="bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
      <!-- 背景装饰 -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary-100 rounded-full opacity-50"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-100 rounded-full opacity-50"></div>
      </div>

      <div class="relative py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-3xl text-center opacity-0 fade-in-up">
            <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900">
              赋能行业
              <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">数智化升级</span>
            </h2>
            <p class="mt-6 text-xl leading-8 text-gray-600">
              AI解决方案将在多个行业实现落地应用，助力企业数字化转型
            </p>
          </div>

          <div class="mx-auto mt-20 grid max-w-2xl grid-cols-1 gap-8 sm:mt-24 lg:mx-0 lg:max-w-none lg:grid-cols-4">
            <div v-for="(industry, index) in industries" :key="industry.name"
                 class="group relative bg-white rounded-2xl p-8 hover-float opacity-0 fade-in-up border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-xl"
                 :class="`delay-${(index + 1) * 100}`">

              <!-- 图标区域 -->
              <div class="relative mb-6">
                <div class="w-16 h-16 bg-gray-50 rounded-2xl flex items-center justify-center group-hover:bg-gray-100 transition-colors duration-300">
                  <component :is="industry.icon" class="h-8 w-8 text-gray-700 group-hover:text-gray-900 transition-colors duration-300" />
                </div>
                <!-- 装饰性小点 -->
                <div class="absolute -top-1 -right-1 w-3 h-3 bg-gray-200 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              <!-- 内容区域 -->
              <div class="space-y-4">
                <h3 class="text-xl font-semibold text-gray-900 group-hover:text-black transition-colors duration-300">
                  {{ industry.name }}
                </h3>
                <p class="text-gray-600 leading-relaxed text-sm group-hover:text-gray-700 transition-colors duration-300">
                  {{ industry.description }}
                </p>
              </div>

              <!-- 悬浮时的装饰线 -->
              <div class="absolute bottom-0 left-8 right-8 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              <!-- 右上角装饰 -->
              <div class="absolute top-6 right-6 w-2 h-2 bg-gray-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Core AI Services Section -->
    <div class="bg-white py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-3xl text-center opacity-0 fade-in-up">
          <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900">
            核心
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">AI能力</span>
          </h2>
          <p class="mt-6 text-xl leading-8 text-gray-600">
            深度掌握前沿AI技术，提供全方位智能化解决方案
          </p>
        </div>

        <div class="mx-auto mt-20 grid max-w-2xl grid-cols-1 gap-8 sm:mt-24 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          <div v-for="(service, index) in coreServices" :key="service.name"
               class="group relative bg-white rounded-2xl p-8 hover-float opacity-0 fade-in-up border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-xl"
               :class="`delay-${(index + 1) * 100}`">

            <!-- 图标区域 -->
            <div class="relative mb-6">
              <div class="w-16 h-16 bg-gray-50 rounded-2xl flex items-center justify-center group-hover:bg-gray-100 transition-colors duration-300">
                <component :is="service.icon" class="h-8 w-8 text-gray-700 group-hover:text-gray-900 transition-colors duration-300" />
              </div>
              <!-- 装饰性小点 -->
              <div class="absolute -top-1 -right-1 w-3 h-3 bg-gray-200 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            <!-- 内容区域 -->
            <div class="space-y-4">
              <h3 class="text-xl font-semibold text-gray-900 group-hover:text-black transition-colors duration-300">
                {{ service.name }}
              </h3>
              <p class="text-gray-600 leading-relaxed text-sm group-hover:text-gray-700 transition-colors duration-300">
                {{ service.description }}
              </p>

              <!-- 技术标签 -->
              <div class="flex flex-wrap gap-2 pt-2">
                <span v-for="tech in service.technologies" :key="tech"
                      class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 group-hover:bg-gray-200 transition-colors duration-300">
                  {{ tech }}
                </span>
              </div>
            </div>

            <!-- 悬浮时的装饰线 -->
            <div class="absolute bottom-0 left-8 right-8 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <!-- 右上角装饰 -->
            <div class="absolute top-6 right-6 w-2 h-2 bg-gray-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
        </div>

        <!-- 底部说明 -->
        <div class="mx-auto mt-16 max-w-3xl text-center opacity-0 fade-in-up delay-600">
          <div class="bg-gray-50 rounded-2xl p-6">
            <p class="text-gray-600 text-sm leading-relaxed">
              我们拥有资深的AI技术团队，具备从算法研发到产品落地的全链条能力，
              <span class="font-medium text-gray-900">为企业提供定制化的AI解决方案</span>，
              助力业务创新与数字化转型。
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Section -->
    <div id="contact" class="py-24 sm:py-32 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
      <!-- 背景装饰 -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-0 right-0 w-96 h-96 bg-primary-100 rounded-full opacity-30 transform translate-x-1/2 -translate-y-1/2"></div>
        <div class="absolute bottom-0 left-0 w-96 h-96 bg-blue-100 rounded-full opacity-30 transform -translate-x-1/2 translate-y-1/2"></div>
      </div>

      <div class="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-3xl text-center opacity-0 fade-in-up">
          <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900">
            预约
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">专业咨询</span>
          </h2>
          <p class="mt-6 text-xl leading-8 text-gray-600">
            填写以下信息，我们的AI专家将尽快与您联系，为您提供专业的解决方案
          </p>
        </div>

        <div class="mx-auto mt-20 max-w-7xl">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <!-- 联系信息 -->
            <div class="opacity-0 fade-in-left delay-200">
              <div class="bg-white rounded-2xl p-8 border border-gray-100 h-full">
                <div class="mb-8">
                  <h3 class="text-2xl font-bold text-gray-900 mb-2">联系方式</h3>
                  <p class="text-gray-600">多种方式联系我们，获得专业服务</p>
                </div>

                <div class="space-y-6">
                  <div v-for="(contact, index) in contactInfo" :key="contact.name"
                       class="flex items-start gap-4 p-4 rounded-xl hover:bg-gray-50 transition-colors duration-300 opacity-0 fade-in-up"
                       :class="`delay-${300 + index * 100}`">
                    <div class="flex-shrink-0">
                      <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center">
                        <component :is="contact.icon" class="h-6 w-6 text-gray-700" aria-hidden="true" />
                      </div>
                    </div>
                    <div class="flex-1 min-w-0">
                      <dt class="text-base font-semibold text-gray-900">{{ contact.name }}</dt>
                      <dd class="mt-1 text-gray-600 text-sm">{{ contact.value }}</dd>
                    </div>
                  </div>
                </div>

                <!-- 服务承诺 -->
                <div class="mt-8 p-4 bg-gray-50 rounded-xl">
                  <div class="flex items-start gap-3">
                    <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                    <div>
                      <p class="text-sm font-medium text-gray-900">24小时内回复</p>
                      <p class="text-xs text-gray-600 mt-1">我们承诺在收到您的咨询后24小时内给予专业回复</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 咨询表单 -->
            <div class="opacity-0 fade-in-right delay-400">
              <div class="bg-white rounded-2xl p-8 border border-gray-100 h-full">
                <div class="mb-8">
                  <h3 class="text-2xl font-bold text-gray-900 mb-2">咨询预约</h3>
                  <p class="text-gray-600">填写信息，获得专业AI解决方案咨询</p>
                </div>

                <form @submit.prevent="handleConsultationSubmit" class="space-y-6">
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label for="name" class="block text-sm font-medium text-gray-900 mb-2">姓名 *</label>
                      <input v-model="form.name" type="text" name="name" id="name" required
                             placeholder="请输入您的姓名"
                             class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-gray-900 focus:border-transparent transition-all duration-200 text-sm">
                    </div>
                    <div>
                      <label for="company" class="block text-sm font-medium text-gray-900 mb-2">公司名称</label>
                      <input v-model="form.company" type="text" name="company" id="company"
                             placeholder="请输入公司名称"
                             class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-gray-900 focus:border-transparent transition-all duration-200 text-sm">
                    </div>
                  </div>

                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label for="phone" class="block text-sm font-medium text-gray-900 mb-2">联系电话 *</label>
                      <input v-model="form.phone" type="tel" name="phone" id="phone" required
                             placeholder="请输入手机号码"
                             class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-gray-900 focus:border-transparent transition-all duration-200 text-sm">
                    </div>
                    <div>
                      <label for="email" class="block text-sm font-medium text-gray-900 mb-2">电子邮箱 *</label>
                      <input v-model="form.email" type="email" name="email" id="email" required
                             placeholder="请输入邮箱地址"
                             class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-gray-900 focus:border-transparent transition-all duration-200 text-sm">
                    </div>
                  </div>

                  <div>
                    <label for="message" class="block text-sm font-medium text-gray-900 mb-2">咨询内容</label>
                    <textarea v-model="form.message" name="message" id="message" rows="4"
                              placeholder="请描述您的需求或问题，我们将为您提供专业的解决方案..."
                              class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-gray-900 focus:border-transparent transition-all duration-200 text-sm resize-none"></textarea>
                  </div>

                  <div class="pt-2">
                    <button type="submit" class="w-full bg-gray-900 text-white px-6 py-4 rounded-xl font-medium hover:bg-black transition-colors duration-300 flex items-center justify-center gap-2 group">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                      </svg>
                      提交咨询
                      <svg class="ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                      </svg>
                    </button>
                  </div>

                  <p class="text-xs text-gray-500 text-center pt-2">
                    提交表单即表示您同意我们的
                    <a href="#" class="text-gray-700 hover:text-gray-900 underline">隐私政策</a>
                    和
                    <a href="#" class="text-gray-700 hover:text-gray-900 underline">服务条款</a>
                  </p>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  AcademicCapIcon,
  BeakerIcon,
  BanknotesIcon,
  CubeIcon,
  BuildingOfficeIcon,
  ClockIcon,
  EnvelopeIcon,
  PhoneIcon,
  CpuChipIcon,
  ChatBubbleLeftRightIcon,
  EyeIcon,
  UserIcon,
  ChartBarIcon,
  CogIcon
} from '@heroicons/vue/24/outline'

const industries = [
  {
    name: '智慧教育',
    description: '智能教培系统，个性化学习体验，均衡区域教育资源',
    icon: AcademicCapIcon
  },
  {
    name: '数字医疗',
    description: '海量病例库，辅助智能问诊，助力精准医疗发展',
    icon: BeakerIcon
  },
  {
    name: '数字金融',
    description: '深入网点场景，数据赋能风控决策，提升产业韧性',
    icon: BanknotesIcon
  },
  {
    name: '智能制造',
    description: '数字化供应链及生产管理，快速实现模块化减碳，实现制造升级',
    icon: CubeIcon
  }
]

const coreServices = [
  {
    name: '机器学习模型开发',
    description: '基于深度学习框架，构建高精度预测模型，支持监督学习、无监督学习和强化学习等多种算法',
    icon: CpuChipIcon,
    technologies: ['TensorFlow', 'PyTorch', '深度学习', '模型优化']
  },
  {
    name: '自然语言处理',
    description: '提供文本分析、语义理解、智能对话等NLP服务，支持多语言处理和领域定制化',
    icon: ChatBubbleLeftRightIcon,
    technologies: ['BERT', 'GPT', '语义分析', '智能对话']
  },
  {
    name: '计算机视觉应用',
    description: '图像识别、目标检测、人脸识别等视觉AI技术，广泛应用于安防、医疗、制造等领域',
    icon: EyeIcon,
    technologies: ['OpenCV', 'YOLO', '图像识别', '目标检测']
  },
  {
    name: 'AI智能体定制',
    description: '构建智能化AI Agent，实现自主决策和任务执行，提升业务流程自动化水平',
    icon: UserIcon,
    technologies: ['Agent框架', '决策算法', '任务规划', '多模态交互']
  },
  {
    name: '数据分析与预测',
    description: '运用先进的数据挖掘和预测算法，为企业提供精准的业务洞察和趋势预测',
    icon: ChartBarIcon,
    technologies: ['数据挖掘', '预测分析', '商业智能', '可视化']
  },
  {
    name: 'AI系统集成',
    description: '提供端到端的AI解决方案，包括系统架构设计、模型部署、性能优化和运维支持',
    icon: CogIcon,
    technologies: ['系统架构', '模型部署', 'MLOps', '云原生']
  }
]

const contactInfo = [
  {
    name: '地址',
    value: '成都市高新区府城大道西段399号',
    icon: BuildingOfficeIcon
  },
  {
    name: '工作时间',
    value: '周一至周五 09:00-18:00',
    icon: ClockIcon
  },
  {
    name: '电话',
    value: '17340101423',
    icon: PhoneIcon
  },
  {
    name: '邮箱',
    value: '<EMAIL>',
    icon: EnvelopeIcon
  }
]

const form = ref({
  name: '',
  company: '',
  phone: '',
  email: '',
  message: ''
})

const handleConsultationSubmit = () => {
  console.log('表单已提交:', form.value)
  alert('表单已提交（请在控制台查看数据）。实际提交逻辑需要您根据后端接口实现。')
  form.value = { name: '', company: '', phone: '', email: '', message: '' }
}
</script>

<style scoped>
.bg-grid-white {
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 添加平滑滚动效果 */
html {
  scroll-behavior: smooth;
}

/* 确保动画只播放一次 */
.opacity-0 {
  opacity: 0;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}
/* 为动画添加延迟 */
.delay-200 { animation-delay: 0.2s; }
.delay-400 { animation-delay: 0.4s; }
</style>