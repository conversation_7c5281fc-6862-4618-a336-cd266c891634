<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Hero Section with Slogan -->
    <div class="relative isolate h-screen overflow-hidden">
      <!-- 背景图片 -->
      <div class="absolute inset-0">
        <img src="/images/hero-bg.jpg" alt="背景" class="w-full h-full object-cover scale-105 animate-pulse-slow" />
        <div class="absolute inset-0 bg-gradient-to-br from-black/50 via-black/40 to-black/60"></div>
      </div>

      <!-- 装饰性几何图形 -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-20 left-10 w-32 h-32 bg-primary-500/20 rounded-full blur-xl animate-bounce-slow"></div>
        <div class="absolute bottom-20 right-10 w-48 h-48 bg-blue-500/20 rounded-full blur-xl animate-pulse-slow"></div>
        <div class="absolute top-1/2 left-1/4 w-24 h-24 bg-purple-500/20 rounded-full blur-lg animate-bounce-slow delay-500"></div>
      </div>

      <div class="relative h-full w-full flex items-center justify-center">
        <div class="max-w-4xl px-6 text-center">
          <h1 class="text-5xl lg:text-7xl font-bold tracking-tight text-white drop-shadow-2xl opacity-0 fade-in-up">
            <span class="block">AI赋能产业</span>
            <span class="block text-gradient bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mt-2">数字化革新</span>
          </h1>
          <p class="mt-8 text-xl lg:text-2xl leading-relaxed text-gray-100 drop-shadow-lg opacity-0 fade-in-up delay-200 max-w-3xl mx-auto">
            囤鼠科技致力于吸收前沿AI科技，深入行业需求场景，搭建核心技术与行业应用的桥梁，提供领先的智能化解决方案，为企业数字变革保驾护航。
          </p>
          <div class="mt-12 flex flex-col sm:flex-row items-center justify-center gap-6 opacity-0 fade-in-up delay-400">
            <a href="#contact" class="btn-primary group">
              立即咨询
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
              </svg>
            </a>
            <router-link to="/about/company" class="btn-outline group">
              了解更多
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
              </svg>
            </router-link>
          </div>


        </div>
      </div>
    </div>

    <!-- Industries Section -->
    <div class="bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
      <!-- 背景装饰 -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary-100 rounded-full opacity-50"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-100 rounded-full opacity-50"></div>
      </div>

      <div class="relative py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-3xl text-center opacity-0 fade-in-up">
            <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900">
              赋能行业
              <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">数智化升级</span>
            </h2>
            <p class="mt-6 text-xl leading-8 text-gray-600">
              AI解决方案将在多个行业实现落地应用，助力企业数字化转型
            </p>
          </div>

          <div class="mx-auto mt-20 grid max-w-2xl grid-cols-1 gap-8 sm:mt-24 lg:mx-0 lg:max-w-none lg:grid-cols-4">
            <div v-for="(industry, index) in industries" :key="industry.name"
                 class="card group hover-float opacity-0 fade-in-up"
                 :class="`delay-${(index + 1) * 100}`">
              <div class="aspect-[4/3] bg-gradient-to-br from-primary-500 to-primary-700 overflow-hidden relative">
                <!-- 背景图案 -->
                <div class="absolute inset-0 opacity-10">
                  <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                    <defs>
                      <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                        <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5"/>
                      </pattern>
                    </defs>
                    <rect width="100" height="100" fill="url(#grid)" />
                  </svg>
                </div>

                <div class="relative h-full w-full flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300">
                  <component :is="industry.icon" class="h-16 w-16 drop-shadow-lg" />
                </div>

                <!-- 悬浮效果 -->
                <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              <div class="flex-1 p-6">
                <h3 class="text-xl font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">
                  {{ industry.name }}
                </h3>
                <p class="mt-3 text-gray-600 leading-relaxed">{{ industry.description }}</p>

                <!-- 了解更多按钮 -->
                <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <span class="text-primary-600 text-sm font-medium flex items-center">
                    了解更多
                    <svg class="ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Section -->
    <div id="contact" class="py-24 sm:py-32 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
      <!-- 背景装饰 -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-0 right-0 w-96 h-96 bg-primary-100 rounded-full opacity-30 transform translate-x-1/2 -translate-y-1/2"></div>
        <div class="absolute bottom-0 left-0 w-96 h-96 bg-blue-100 rounded-full opacity-30 transform -translate-x-1/2 translate-y-1/2"></div>
      </div>

      <div class="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-3xl text-center opacity-0 fade-in-up">
          <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900">
            预约
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">专业咨询</span>
          </h2>
          <p class="mt-6 text-xl leading-8 text-gray-600">
            填写以下信息，我们的AI专家将尽快与您联系，为您提供专业的解决方案
          </p>
        </div>

        <div class="mx-auto mt-20 grid max-w-none grid-cols-1 gap-12 lg:max-w-7xl lg:grid-cols-2">
          <!-- 联系信息 -->
          <div class="opacity-0 fade-in-left delay-200">
            <div class="card p-8">
              <h3 class="text-2xl font-bold text-gray-900 mb-8 flex items-center">
                <svg class="w-6 h-6 text-primary-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                联系方式
              </h3>

              <dl class="space-y-6">
                <div v-for="(contact, index) in contactInfo" :key="contact.name"
                     class="flex gap-x-4 items-start p-4 rounded-lg hover:bg-gray-50 transition-colors duration-300 opacity-0 fade-in-up"
                     :class="`delay-${300 + index * 100}`">
                  <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                      <component :is="contact.icon" class="h-6 w-6 text-primary-600" aria-hidden="true" />
                    </div>
                  </div>
                  <div>
                    <dt class="text-lg font-semibold text-gray-900">{{ contact.name }}</dt>
                    <dd class="mt-1 text-gray-600 leading-relaxed">{{ contact.value }}</dd>
                  </div>
                </div>
              </dl>

              <!-- 服务时间提示 -->
              <div class="mt-8 p-4 bg-primary-50 rounded-lg border border-primary-100">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-primary-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-sm text-primary-700 font-medium">我们承诺在24小时内回复您的咨询</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 咨询表单 -->
          <div class="opacity-0 fade-in-right delay-400">
            <div class="card p-8">
              <h3 class="text-2xl font-bold text-gray-900 mb-8 flex items-center">
                <svg class="w-6 h-6 text-primary-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                咨询预约
              </h3>

              <form @submit.prevent="handleConsultationSubmit" class="space-y-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div>
                    <label for="name" class="block text-sm font-medium text-gray-900 mb-2">姓名 *</label>
                    <input v-model="form.name" type="text" name="name" id="name" required
                           placeholder="请输入您的姓名"
                           class="input-field">
                  </div>
                  <div>
                    <label for="company" class="block text-sm font-medium text-gray-900 mb-2">公司名称</label>
                    <input v-model="form.company" type="text" name="company" id="company"
                           placeholder="请输入公司名称"
                           class="input-field">
                  </div>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div>
                    <label for="phone" class="block text-sm font-medium text-gray-900 mb-2">联系电话 *</label>
                    <input v-model="form.phone" type="tel" name="phone" id="phone" required
                           placeholder="请输入手机号码"
                           class="input-field">
                  </div>
                  <div>
                    <label for="email" class="block text-sm font-medium text-gray-900 mb-2">电子邮箱 *</label>
                    <input v-model="form.email" type="email" name="email" id="email" required
                           placeholder="请输入邮箱地址"
                           class="input-field">
                  </div>
                </div>

                <div>
                  <label for="message" class="block text-sm font-medium text-gray-900 mb-2">咨询内容</label>
                  <textarea v-model="form.message" name="message" id="message" rows="4"
                            placeholder="请描述您的需求或问题，我们将为您提供专业的解决方案..."
                            class="input-field resize-none"></textarea>
                </div>

                <div>
                  <button type="submit" class="btn-primary w-full group">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                    提交咨询
                    <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                  </button>
                </div>

                <p class="text-xs text-gray-500 text-center">
                  提交表单即表示您同意我们的
                  <a href="#" class="text-primary-600 hover:text-primary-700 underline">隐私政策</a>
                  和
                  <a href="#" class="text-primary-600 hover:text-primary-700 underline">服务条款</a>
                </p>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  AcademicCapIcon,
  BeakerIcon,
  BanknotesIcon,
  CubeIcon,
  BuildingOfficeIcon,
  ClockIcon,
  EnvelopeIcon,
  PhoneIcon,
} from '@heroicons/vue/24/outline'

const industries = [
  {
    name: '智慧教育',
    description: '智能教培系统，个性化学习体验，均衡区域教育资源',
    icon: AcademicCapIcon
  },
  {
    name: '数字医疗',
    description: '海量病例库，辅助智能问诊，助力精准医疗发展',
    icon: BeakerIcon
  },
  {
    name: '数字金融',
    description: '深入网点场景，数据赋能风控决策，提升产业韧性',
    icon: BanknotesIcon
  },
  {
    name: '智能制造',
    description: '数字化供应链及生产管理，快速实现模块化减碳，实现制造升级',
    icon: CubeIcon
  }
]

const contactInfo = [
  {
    name: '地址',
    value: '成都市锦江区华宇广场B座',
    icon: BuildingOfficeIcon
  },
  {
    name: '工作时间',
    value: '周一至周五 9:00-18:00',
    icon: ClockIcon
  },
  {
    name: '电话',
    value: '***********',
    icon: PhoneIcon
  },
  {
    name: '邮箱',
    value: '<EMAIL>',
    icon: EnvelopeIcon
  }
]

const form = ref({
  name: '',
  company: '',
  phone: '',
  email: '',
  message: ''
})

const handleConsultationSubmit = () => {
  console.log('表单已提交:', form.value)
  alert('表单已提交（请在控制台查看数据）。实际提交逻辑需要您根据后端接口实现。')
  form.value = { name: '', company: '', phone: '', email: '', message: '' }
}
</script>

<style scoped>
.bg-grid-white {
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 添加平滑滚动效果 */
html {
  scroll-behavior: smooth;
}

/* 确保动画只播放一次 */
.opacity-0 {
  opacity: 0;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}
/* 为动画添加延迟 */
.delay-200 { animation-delay: 0.2s; }
.delay-400 { animation-delay: 0.4s; }
</style>