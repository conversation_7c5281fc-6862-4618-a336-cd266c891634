<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Hero Section with Slogan -->
    <div class="relative isolate h-screen">
      <!-- 背景图片 -->
      <div class="absolute inset-0 overflow-hidden">
        <img src="/images/hero-bg.jpg" alt="背景" class="w-full h-full object-cover" />
        <div class="absolute inset-0 bg-black/40"></div>
      </div>
      <div class="relative h-full w-full flex items-center justify-center">
        <div class="max-w-2xl px-6 text-center">
          <h1 class="text-5xl lg:text-6xl font-bold tracking-tight text-white drop-shadow-lg opacity-0 fade-in-up">
            AI赋能产业数字化革新
          </h1>
          <p class="mt-8 text-xl leading-8 text-gray-100 drop-shadow-md opacity-0 fade-in-up delay-200">
            囤鼠科技致力于吸收前沿AI科技，深入行业需求场景，搭建核心技术与行业应用的桥梁，提供领先的智能化解决方案，为企业数字变革保驾护航。
          </p>
          <div class="mt-12 flex items-center justify-center gap-x-6 opacity-0 fade-in-up delay-400">
            <a href="#contact" class="rounded-md bg-primary-600 px-5 py-3 text-base font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 transition-colors duration-300">
              立即咨询
            </a>
            <router-link to="/about/company" class="text-base font-semibold leading-6 text-white hover:text-gray-200 transition-colors duration-300">
              了解更多 <span aria-hidden="true" class="group-hover:translate-x-1 inline-block transition-transform duration-300">→</span>
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Industries Section -->
    <div class="bg-white">
      <div class="py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center opacity-0 fade-in-up">
            <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">赋能行业数智化升级</h2>
            <p class="mt-6 text-lg leading-8 text-gray-600">
              AI解决方案将在多个行业实现落地应用，助力企业数字化转型
            </p>
          </div>
          <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4">
            <div v-for="industry in industries" :key="industry.name" class="relative flex flex-col bg-white shadow-lg rounded-lg overflow-hidden group hover:shadow-xl transition-shadow duration-300">
              <div class="aspect-[4/3] bg-gray-100 overflow-hidden">
                <div class="h-full w-full bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center text-white">
                  <component :is="industry.icon" class="h-16 w-16" />
                </div>
              </div>
              <div class="flex-1 p-6">
                <h3 class="text-xl font-semibold text-gray-900">{{ industry.name }}</h3>
                <p class="mt-2 text-sm text-gray-600">{{ industry.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Section -->
    <div id="contact" class="py-24 sm:py-32 bg-gray-50">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
          <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">预约咨询</h2>
          <p class="mt-6 text-lg leading-8 text-gray-600">
            填写以下信息，我们的顾问将尽快与您联系
          </p>
        </div>
        <div class="mx-auto mt-16 grid max-w-none grid-cols-1 gap-x-8 gap-y-16 lg:max-w-7xl lg:grid-cols-2">
          <div>
            <h3 class="text-lg font-semibold leading-8 tracking-tight text-gray-900">联系方式</h3>
            <dl class="mt-6 space-y-6">
              <div v-for="contact in contactInfo" :key="contact.name" class="flex gap-x-3 items-start">
                <component :is="contact.icon" class="h-6 w-6 flex-none text-primary-600 mt-1" aria-hidden="true" />
                <div>
                  <dt class="text-base font-semibold text-gray-900">{{ contact.name }}</dt>
                  <dd class="mt-1 text-base leading-7 text-gray-600">{{ contact.value }}</dd>
                </div>
              </div>
            </dl>
          </div>
          <div class="bg-white shadow-lg rounded-lg p-8">
            <h3 class="text-lg font-semibold leading-8 tracking-tight text-gray-900 mb-6">咨询预约</h3>
            <form @submit.prevent="handleConsultationSubmit" class="space-y-4">
              <div>
                <label for="name" class="block text-sm font-medium leading-6 text-gray-900">姓名</label>
                <div class="mt-1.5">
                  <input v-model="form.name" type="text" name="name" id="name" autocomplete="name" class="block w-full rounded-md border-0 px-3.5 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6">
                </div>
              </div>
              <div>
                <label for="company" class="block text-sm font-medium leading-6 text-gray-900">公司名称</label>
                <div class="mt-1.5">
                  <input v-model="form.company" type="text" name="company" id="company" autocomplete="organization" class="block w-full rounded-md border-0 px-3.5 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6">
                </div>
              </div>
              <div>
                <label for="phone" class="block text-sm font-medium leading-6 text-gray-900">联系电话</label>
                <div class="mt-1.5">
                  <input v-model="form.phone" type="tel" name="phone" id="phone" autocomplete="tel" class="block w-full rounded-md border-0 px-3.5 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6">
                </div>
              </div>
              <div>
                <label for="email" class="block text-sm font-medium leading-6 text-gray-900">电子邮箱</label>
                <div class="mt-1.5">
                  <input v-model="form.email" type="email" name="email" id="email" autocomplete="email" required class="block w-full rounded-md border-0 px-3.5 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6">
                </div>
              </div>
              <div>
                <label for="message" class="block text-sm font-medium leading-6 text-gray-900">咨询内容</label>
                <div class="mt-1.5">
                  <textarea v-model="form.message" name="message" id="message" rows="3" class="block w-full rounded-md border-0 px-3.5 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6"></textarea>
                </div>
              </div>
              <div>
                <button type="submit" class="flex w-full justify-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 transition-colors duration-300">提交咨询</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  AcademicCapIcon,
  BeakerIcon,
  BanknotesIcon,
  CubeIcon,
  BuildingOfficeIcon,
  ClockIcon,
  EnvelopeIcon,
  PhoneIcon,
} from '@heroicons/vue/24/outline'

const industries = [
  {
    name: '智慧教育',
    description: '智能教培系统，个性化学习体验，均衡区域教育资源',
    icon: AcademicCapIcon
  },
  {
    name: '数字医疗',
    description: '海量病例库，辅助智能问诊，助力精准医疗发展',
    icon: BeakerIcon
  },
  {
    name: '数字金融',
    description: '深入网点场景，数据赋能风控决策，提升产业韧性',
    icon: BanknotesIcon
  },
  {
    name: '智能制造',
    description: '数字化供应链及生产管理，快速实现模块化减碳，实现制造升级',
    icon: CubeIcon
  }
]

const contactInfo = [
  {
    name: '地址',
    value: '成都市锦江区华宇广场B座',
    icon: BuildingOfficeIcon
  },
  {
    name: '工作时间',
    value: '周一至周五 9:00-18:00',
    icon: ClockIcon
  },
  {
    name: '电话',
    value: '***********',
    icon: PhoneIcon
  },
  {
    name: '邮箱',
    value: '<EMAIL>',
    icon: EnvelopeIcon
  }
]

const form = ref({
  name: '',
  company: '',
  phone: '',
  email: '',
  message: ''
})

const handleConsultationSubmit = () => {
  console.log('表单已提交:', form.value)
  alert('表单已提交（请在控制台查看数据）。实际提交逻辑需要您根据后端接口实现。')
  form.value = { name: '', company: '', phone: '', email: '', message: '' }
}
</script>

<style scoped>
.bg-grid-white {
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 添加平滑滚动效果 */
html {
  scroll-behavior: smooth;
}

/* 确保动画只播放一次 */
.opacity-0 {
  opacity: 0;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}
/* 为动画添加延迟 */
.delay-200 { animation-delay: 0.2s; }
.delay-400 { animation-delay: 0.4s; }
</style> 