<template>
  <div class="min-h-screen bg-gray-50">
    <PageHeader>
      囤鼠AI大模型一体机
      <template #subtitle>
        专为企业级大模型应用设计的一体化解决方案
      </template>
    </PageHeader>

    <div class="py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="text-center">
          <h2 class="text-3xl font-bold tracking-tight text-primary-600 sm:text-4xl">产品核心优势</h2>
          <p class="mt-4 text-lg text-gray-600">
            集成算力+存储+软件平台的一体式AI落地方案，适用于中小企业和机构部署大模型服务
          </p>
        </div>

        <div class="mx-auto mt-20 max-w-2xl sm:mt-24 lg:mt-28 lg:max-w-none">
          <dl class="grid grid-cols-1 gap-8 lg:grid-cols-3">
            <div v-for="(feature, index) in features" :key="feature.name"
                 class="card group hover-float p-8 opacity-0 fade-in-up"
                 :class="`delay-${(index + 1) * 100}`">
              <dt class="flex flex-col items-center gap-6">
                <div class="relative">
                  <!-- 背景光效 -->
                  <div class="absolute inset-0 bg-primary-500/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                  <div class="relative flex h-20 w-20 items-center justify-center rounded-xl bg-gradient-to-br from-primary-100 to-primary-200 group-hover:from-primary-200 group-hover:to-primary-300 transition-all duration-300 shadow-lg">
                    <component :is="feature.icon" class="h-10 w-10 text-primary-600 group-hover:scale-110 transition-transform duration-300" aria-hidden="true" />
                  </div>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{ feature.name }}</h3>
              </dt>
              <dd class="mt-6 text-center text-gray-600 leading-relaxed">{{ feature.description }}</dd>

              <!-- 悬浮时显示的装饰 -->
              <div class="mt-6 flex justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="w-12 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full"></div>
              </div>
            </div>
          </dl>
        </div>

        <div class="mt-32">
          <div class="text-center mb-20 opacity-0 fade-in-up">
            <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900">
              技术规格
              <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">参数</span>
            </h2>
            <p class="mt-6 text-xl text-gray-600">多种配置可选，满足不同规模企业需求</p>
          </div>

          <div class="card overflow-hidden opacity-0 fade-in-up delay-200">
            <div class="overflow-x-auto">
              <table class="min-w-full">
                <thead>
                  <tr class="bg-gradient-to-r from-primary-600 to-blue-600">
                    <th class="py-6 px-8 text-left text-lg font-bold text-white">规格配置</th>
                    <th class="py-6 px-8 text-left text-lg font-bold text-white">
                      <div class="flex items-center">
                        <span>基础版</span>
                        <span class="ml-2 px-2 py-1 bg-white/20 rounded-full text-xs">入门</span>
                      </div>
                    </th>
                    <th class="py-6 px-8 text-left text-lg font-bold text-white">
                      <div class="flex items-center">
                        <span>专业版</span>
                        <span class="ml-2 px-2 py-1 bg-white/20 rounded-full text-xs">推荐</span>
                      </div>
                    </th>
                    <th class="py-6 px-8 text-left text-lg font-bold text-white">
                      <div class="flex items-center">
                        <span>企业版</span>
                        <span class="ml-2 px-2 py-1 bg-white/20 rounded-full text-xs">旗舰</span>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-100">
                  <tr v-for="(spec, index) in specifications" :key="spec.name"
                      class="hover:bg-gray-50 transition-colors duration-200"
                      :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'">
                    <td class="py-5 px-8 text-base font-semibold text-gray-900">{{ spec.name }}</td>
                    <td class="py-5 px-8 text-base text-gray-700">{{ spec.basic }}</td>
                    <td class="py-5 px-8 text-base text-gray-700 relative">
                      {{ spec.pro }}
                      <!-- 推荐标识 -->
                      <div v-if="index === 0" class="absolute -top-2 -right-2">
                        <div class="w-3 h-3 bg-primary-500 rounded-full animate-pulse"></div>
                      </div>
                    </td>
                    <td class="py-5 px-8 text-base text-gray-700">{{ spec.enterprise }}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 表格底部说明 -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-8 py-6 border-t border-gray-200">
              <div class="flex items-center justify-center text-sm text-gray-600">
                <svg class="w-5 h-5 text-primary-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                所有配置均支持定制化调整，具体规格请联系我们的技术顾问
              </div>
            </div>
          </div>
        </div>

        <div class="mt-32">
          <div class="text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-primary-600 sm:text-4xl">核心应用</h2>
          </div>

          <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
            <div v-for="app in applications" :key="app.title" class="rounded-2xl bg-white p-8 shadow-lg">
              <div class="relative h-12">
                <div class="absolute top-0 left-0 bg-primary-100 text-primary-600 rounded-lg px-4 py-2 text-sm font-semibold">
                  {{ app.id }}
                </div>
              </div>
              <h3 class="mt-8 text-xl font-semibold text-gray-900">{{ app.title }}</h3>
              <ul class="mt-4 space-y-2">
                <li v-for="feature in app.features" :key="feature" class="text-gray-600">{{ feature }}</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="mt-32">
          <div class="text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-primary-600 sm:text-4xl">应用场景</h2>
          </div>

          <div class="grid grid-cols-1 gap-8 lg:grid-cols-4">
            <div v-for="scene in scenes" :key="scene.title"
                 class="flex flex-col items-center p-6 rounded-2xl bg-white shadow-lg hover:shadow-xl transition-shadow duration-300">
              <component :is="scene.icon" class="h-12 w-12 text-primary-600" aria-hidden="true" />
              <h3 class="mt-4 text-lg font-medium text-gray-900">{{ scene.title }}</h3>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  BoltIcon,
  CloudArrowUpIcon,
  ShieldCheckIcon,
  AdjustmentsHorizontalIcon,
  CpuChipIcon,
  UserGroupIcon,
  BeakerIcon,
  LockClosedIcon,
  SunIcon,
  AcademicCapIcon
} from '@heroicons/vue/24/outline'
import PageHeader from '@/components/PageHeader.vue'

defineOptions({
  name: 'AIServerView'
})

const features = [
  {
    name: '高性能计算',
    description: '搭载最新GPU计算单元，提供强大的并行计算能力，支持千亿参数大模型高效推理。',
    icon: BoltIcon
  },
  {
    name: '开箱即用',
    description: '预装优化后的大模型环境，无需复杂配置，通电即可使用，大幅降低技术门槛。',
    icon: CloudArrowUpIcon
  },
  {
    name: '安全可靠',
    description: '内置数据加密与访问控制机制，确保企业数据安全，支持私有化部署。',
    icon: ShieldCheckIcon
  },
  {
    name: '灵活扩展',
    description: '模块化设计支持横向扩展，可根据业务需求灵活调整计算资源。',
    icon: AdjustmentsHorizontalIcon
  },
  {
    name: '智能优化',
    description: '内置模型压缩与量化技术，在保证精度的同时提升推理速度。',
    icon: CpuChipIcon
  },
  {
    name: '专业支持',
    description: '提供7x24小时技术支持，专家团队全程护航您的AI应用。',
    icon: UserGroupIcon
  }
] as const

const specifications = [
  {
    name: '处理器',
    basic: '双路Intel Xeon Silver 4310',
    pro: '双路Intel Xeon Gold 6330',
    enterprise: '四路AMD EPYC 7763'
  },
  {
    name: 'GPU配置',
    basic: 'NVIDIA A30 x2',
    pro: 'NVIDIA A100 40GB x4',
    enterprise: 'NVIDIA H100 80GB x8'
  },
  {
    name: '内存容量',
    basic: '256GB DDR4',
    pro: '512GB DDR4',
    enterprise: '2TB DDR4'
  },
  {
    name: '存储配置',
    basic: '4TB NVMe SSD',
    pro: '8TB NVMe SSD + 20TB HDD',
    enterprise: '16TB NVMe SSD + 50TB HDD'
  },
  {
    name: '支持模型规模',
    basic: '≤10B参数',
    pro: '≤100B参数',
    enterprise: '≤500B参数'
  },
  {
    name: '并发推理能力',
    basic: '50~100请求/秒',
    pro: '200~500请求/秒',
    enterprise: '1000+请求/秒'
  },
  {
    name: '电源',
    basic: '1600W冗余电源',
    pro: '2000W冗余电源',
    enterprise: '3000W冗余电源'
  }
] as const

const applications = [
  {
    id: '01',
    title: '综合管理类',
    features: [
      '多模态处理：具备视频图像与文本理解、视觉问答和图像生成检测等能力',
      'Agent：丰富智能体样例，快速起步，灵活定制，轻松编排',
      '用户管理：支持新增用户，用户状态管理，权限管理'
    ]
  },
  {
    id: '02',
    title: '会议办公类',
    features: [
      '会议助手：自动采制会议内容，生成详细会议摘要和代办事项',
      '编码助手：智能生成代码，提高开发效率',
      '工作指导：支持企业工作任务智能分解，确保工作计划的系统化'
    ]
  },
  {
    id: '03',
    title: '信息处理类',
    features: [
      '智能问答：快速获取准确答案，提高信息检索效率',
      '长文总结：精准提炼文本核心要点，快速生成总结',
      '知识库管理：运用RAG技术，智能检索'
    ]
  }
] as const

const scenes = [
  {
    title: '智慧医疗',
    icon: BeakerIcon
  },
  {
    title: '公共安全',
    icon: LockClosedIcon
  },
  {
    title: '绿色能源',
    icon: SunIcon
  },
  {
    title: '智慧教育',
    icon: AcademicCapIcon
  }
] as const
</script>

<style scoped>
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}
</style>