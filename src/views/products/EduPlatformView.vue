<template>
  <div class="min-h-screen bg-gray-50">
    <PageHeader>
      TunShuEdu AI智教平台
      <template #subtitle>
        基于大模型AI技术，为教育咨询机构提供全方位的智能化服务
      </template>
    </PageHeader>

    <div class="py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <!-- 产品优势 -->
        <div class="text-center">
          <h2 class="text-3xl font-bold tracking-tight text-primary-600 sm:text-4xl">产品优势</h2>
          <p class="mt-4 text-lg text-gray-600">
            TunShuEdu集成了最先进的大语言模型，具备处理多模态信息的能力，同时本地部署数据库，杜绝信息价值泄漏。
          </p>
        </div>

        <!-- 核心价值功能 -->
        <div class="mt-32">
          <div class="text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-primary-600 sm:text-4xl">核心价值功能</h2>
          </div>

          <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div v-for="feature in features" :key="feature.name" class="flex flex-col items-center">
                <dt class="text-base font-semibold leading-7 text-gray-900">
                  <div class="mb-6 flex h-16 w-16 items-center justify-center rounded-lg bg-primary-100">
                    <component :is="feature.icon" class="h-8 w-8 text-primary-600" aria-hidden="true" />
                  </div>
                  {{ feature.name }}
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600 text-center">
                  <p class="flex-auto">{{ feature.description }}</p>
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <!-- 应用场景 -->
        <div class="mt-32">
          <div class="text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-primary-600 sm:text-4xl">应用场景</h2>
          </div>
          <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
            <div v-for="scene in scenes" :key="scene.title" 
                 class="flex flex-col bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div class="p-8">
                <div class="flex items-center mb-6">
                  <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-primary-100">
                    <component :is="scene.icon" class="h-6 w-6 text-primary-600" aria-hidden="true" />
                  </div>
                  <h3 class="ml-4 text-xl font-semibold text-gray-900">{{ scene.title }}</h3>
                </div>
                <p class="text-gray-600">{{ scene.description }}</p>
                <ul class="mt-6 space-y-3">
                  <li v-for="point in scene.points" :key="point" class="flex gap-x-3">
                    <CheckCircleIcon class="h-6 w-5 flex-none text-primary-600" aria-hidden="true" />
                    <span class="text-sm text-gray-600">{{ point }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  AcademicCapIcon,
  ChartBarIcon,
  ClipboardDocumentCheckIcon,
  BuildingLibraryIcon,
  UserGroupIcon,
  CheckCircleIcon,
  BookOpenIcon,
  GlobeAltIcon,
  AcademicCapIcon as AcademicCapIcon2
} from '@heroicons/vue/24/outline'
import PageHeader from '@/components/PageHeader.vue'

const features = [
  {
    name: '客制化学习方案',
    description: '基于大模型的智能学习系统，通过评估用户背景，针对性制定学习方案，调用生成学习资料。',
    icon: AcademicCapIcon,
  },
  {
    name: '智能文案生成',
    description: '便捷使用，仅需少量提示词，即可深度调用数据库，为用户生成文案材料。',
    icon: ClipboardDocumentCheckIcon,
  },
  {
    name: '实况数据检测',
    description: '全方位监控主流数据库状态，即时更新，保障用户使用效率。',
    icon: ChartBarIcon,
  },
]

const scenes = [
  {
    title: '高校',
    description: '为高等院校提供智能化教学和管理解决方案，提升教学质量和管理效率。',
    icon: BuildingLibraryIcon,
    points: [
      '智能教学助手，辅助课程设计与教学',
      '学生学习分析，个性化教育指导',
      '科研写作助手，提升论文质量',
      '智能教务管理，优化资源配置'
    ]
  },
  {
    title: '留学机构',
    description: '为留学咨询机构提供全方位的智能化服务支持，提升咨询和服务质量。',
    icon: GlobeAltIcon,
    points: [
      '智能文书生成，提供专业写作建议',
      '院校选择分析，精准匹配推荐',
      '留学规划指导，定制化方案制定',
      '辅助资源收集，大幅提升offer率'
    ]
  },
  {
    title: '职业培训学校',
    description: '为职业培训机构提供智能化培训解决方案，提升培训效果和就业竞争力。',
    icon: AcademicCapIcon2,
    points: [
      '智能课程规划，紧跟行业需求',
      '实践技能评估，个性化指导',
      '就业能力分析，精准职业规划',
      '企业需求对接，提升就业率'
    ]
  }
]
</script>

<style scoped>
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
</style> 