<template>
  <div class="min-h-screen bg-gray-50">
    <PageHeader>
      关于我们
      <template #subtitle>
        致力于通过人工智能技术推动产业数字化转型，为企业提供智能化解决方案，打破生产力瓶颈
      </template>
    </PageHeader>

    <!-- 公司简介 -->
    <div class="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
      <div class="mx-auto max-w-4xl text-center opacity-0 fade-in-up">
        <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900">
          使命
          <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">愿景</span>
        </h2>
        <p class="mt-8 text-xl leading-relaxed text-gray-600 max-w-3xl mx-auto">
          让AI深入社会和人类生活，切实推动社会更安全更宜居，赋能智慧运营，迈进可持续发展。
        </p>

        <!-- 装饰性分隔线 -->
        <div class="mt-12 flex justify-center">
          <div class="w-24 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full"></div>
        </div>
      </div>

      <!-- 核心价值观 -->
      <div class="mx-auto mt-24 max-w-2xl sm:mt-32 lg:max-w-none">
        <div class="text-center mb-16 opacity-0 fade-in-up delay-200">
          <h3 class="text-3xl lg:text-4xl font-bold tracking-tight text-gray-900">
            核心
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">价值观</span>
          </h3>
          <p class="mt-4 text-lg text-gray-600">指导我们前进的核心理念</p>
        </div>

        <dl class="grid max-w-xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-3">
          <div v-for="(value, index) in values" :key="value.name"
               class="card group hover-float p-8 text-center opacity-0 fade-in-up"
               :class="`delay-${300 + index * 100}`">
            <dt class="flex flex-col items-center">
              <div class="relative mb-6">
                <!-- 背景光效 -->
                <div class="absolute inset-0 bg-primary-500/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div class="relative flex h-16 w-16 items-center justify-center rounded-xl bg-gradient-to-br from-primary-500 to-blue-600 group-hover:from-primary-600 group-hover:to-blue-700 transition-all duration-300 shadow-lg">
                  <component :is="value.icon" class="h-8 w-8 text-white group-hover:scale-110 transition-transform duration-300" aria-hidden="true" />
                </div>
              </div>
              <h4 class="text-xl font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">
                {{ value.name }}
              </h4>
            </dt>
            <dd class="mt-4 text-gray-600 leading-relaxed">
              {{ value.description }}
            </dd>

            <!-- 悬浮时显示的装饰 -->
            <div class="mt-6 flex justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div class="w-12 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full"></div>
            </div>
          </div>
        </dl>
      </div>

      <!-- 发展历程 -->
      <div class="mx-auto mt-24 max-w-4xl sm:mt-32">
        <div class="text-center mb-16 opacity-0 fade-in-up delay-600">
          <h3 class="text-3xl lg:text-4xl font-bold tracking-tight text-gray-900">
            发展
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">历程</span>
          </h3>
          <p class="mt-4 text-lg text-gray-600">见证我们的成长足迹</p>
        </div>

        <div class="relative">
          <!-- 时间线背景 -->
          <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary-500 via-blue-500 to-purple-500"></div>

          <div class="space-y-12">
            <div v-for="(milestone, milestoneIdx) in milestones" :key="milestone.year"
                 class="relative opacity-0 fade-in-up"
                 :class="`delay-${700 + milestoneIdx * 100}`">
              <!-- 时间节点 -->
              <div class="absolute left-6 w-4 h-4 bg-white border-4 border-primary-500 rounded-full shadow-lg"></div>

              <!-- 内容卡片 -->
              <div class="ml-20">
                <div class="card group hover-float p-6">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="flex items-center space-x-3 mb-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-blue-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                          <component :is="milestone.icon" class="h-5 w-5 text-white" aria-hidden="true" />
                        </div>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800">
                          {{ milestone.year }}
                        </span>
                      </div>
                      <p class="text-gray-700 leading-relaxed group-hover:text-gray-900 transition-colors duration-300">
                        {{ milestone.content }}
                      </p>
                    </div>
                  </div>

                  <!-- 悬浮时显示的装饰 -->
                  <div class="mt-4 flex justify-start opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div class="w-16 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LightBulbIcon, UserGroupIcon, ChartBarIcon, RocketLaunchIcon, BuildingOfficeIcon, TrophyIcon } from '@heroicons/vue/24/outline'
import PageHeader from '@/components/PageHeader.vue'

const values = [
  {
    name: '创新驱动',
    description: '持续推动技术创新，行业发展方向，为客户带来革新性的解决方案。',
    icon: LightBulbIcon,
  },
  {
    name: '需求至上',
    description: '以产业需求为中心，提供专业、高效的服务，助力业务增长。',
    icon: UserGroupIcon,
  },
  {
    name: '协作共赢',
    description: '与产业用户建立长期稳定的伙伴关系，共同创造价值，实现可持续发展。',
    icon: ChartBarIcon,
  },
]

const milestones = [
  {
    year: '2025.5',
    content: 'TunShuEdu AI智教平台Alpha版本,逐鹿市场',
    icon: TrophyIcon,
  },
  {
    year: '2025.4',
    content: '完成市场探索，深谙需求缺口，目标打造客制化企业赋能',
    icon: RocketLaunchIcon,
  },
  {
    year: '2025.3',
    content: '公司成立，明确价值观与服务宗旨',
    icon: BuildingOfficeIcon,
  },
]
</script>

<style scoped>
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
</style>