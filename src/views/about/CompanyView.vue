<template>
  <div class="min-h-screen bg-gray-50">
    <PageHeader>
      关于我们
      <template #subtitle>
        致力于通过人工智能技术推动产业数字化转型，为企业提供智能化解决方案，打破生产力瓶颈
      </template>
    </PageHeader>

    <!-- 公司简介 -->
    <div class="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
      <div class="mx-auto max-w-2xl lg:mx-0">
        <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">使命愿景</h2>
        <p class="mt-6 text-lg leading-8 text-gray-600">
          让AI深入社会和人类生活，切实推动社会更安全更宜居，赋能智慧运营，迈进可持续发展。
        </p>
      </div>

      <!-- 核心价值观 -->
      <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <h3 class="text-2xl font-bold tracking-tight text-gray-900 mb-8">核心价值观</h3>
        <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
          <div v-for="value in values" :key="value.name" class="flex flex-col">
            <dt class="text-base font-semibold leading-7 text-gray-900">
              <div class="mb-6 flex h-10 w-10 items-center justify-center rounded-lg bg-indigo-600">
                <component :is="value.icon" class="h-6 w-6 text-white" aria-hidden="true" />
              </div>
              {{ value.name }}
            </dt>
            <dd class="mt-1 flex flex-auto flex-col text-base leading-7 text-gray-600">
              <p class="flex-auto">{{ value.description }}</p>
            </dd>
          </div>
        </dl>
      </div>

      <!-- 发展历程 -->
      <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <h3 class="text-2xl font-bold tracking-tight text-gray-900 mb-8">发展历程</h3>
        <div class="flow-root">
          <ul role="list" class="-mb-8">
            <li v-for="(milestone, milestoneIdx) in milestones" :key="milestone.year">
              <div class="relative pb-8">
                <span v-if="milestoneIdx !== milestones.length - 1" class="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                <div class="relative flex space-x-3">
                  <div>
                    <span class="h-8 w-8 rounded-full bg-indigo-600 flex items-center justify-center ring-8 ring-white">
                      <component :is="milestone.icon" class="h-5 w-5 text-white" aria-hidden="true" />
                    </span>
                  </div>
                  <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                    <div>
                      <p class="text-sm text-gray-500">{{ milestone.content }}</p>
                    </div>
                    <div class="whitespace-nowrap text-right text-sm text-gray-500">
                      <time>{{ milestone.year }}</time>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LightBulbIcon, UserGroupIcon, ChartBarIcon, RocketLaunchIcon, BuildingOfficeIcon, TrophyIcon } from '@heroicons/vue/24/outline'
import PageHeader from '@/components/PageHeader.vue'

const values = [
  {
    name: '创新驱动',
    description: '持续推动技术创新，行业发展方向，为客户带来革新性的解决方案。',
    icon: LightBulbIcon,
  },
  {
    name: '需求至上',
    description: '以产业需求为中心，提供专业、高效的服务，助力业务增长。',
    icon: UserGroupIcon,
  },
  {
    name: '协作共赢',
    description: '与产业用户建立长期稳定的伙伴关系，共同创造价值，实现可持续发展。',
    icon: ChartBarIcon,
  },
]

const milestones = [
  {
    year: '2025.5',
    content: 'TunShuEdu AI智教平台Alpha版本,逐鹿市场',
    icon: TrophyIcon,
  },
  {
    year: '2025.4',
    content: '完成市场探索，深谙需求缺口，目标打造客制化企业赋能',
    icon: RocketLaunchIcon,
  },
  {
    year: '2025.3',
    content: '公司成立，明确价值观与服务宗旨',
    icon: BuildingOfficeIcon,
  },
]
</script>

<style scoped>
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
</style> 