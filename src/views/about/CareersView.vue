<template>
  <div class="min-h-screen bg-gray-50">
    <PageHeader>
      加入我们
      <template #subtitle>
        探索AI的无限可能，共同推动产业数字化转型
      </template>
    </PageHeader>

    <div class="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
      <!-- Introduction -->
      <div class="mx-auto max-w-2xl lg:mx-0">
        <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">工作机会</h2>
        <p class="mt-6 text-lg leading-8 text-gray-600">
          渴望充满激情的伙伴加入我们的团队，一起描绘AI应用的蓝图。
        </p>
      </div>

      <!-- Positions -->
      <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <div class="grid grid-cols-1 gap-x-8 gap-y-16 lg:grid-cols-2">
          <div v-for="position in positions" :key="position.title" class="flex flex-col bg-white rounded-2xl shadow-lg overflow-hidden">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-indigo-600">
                  <component :is="position.icon" class="h-6 w-6 text-white" aria-hidden="true" />
                </div>
                <h3 class="ml-4 text-xl font-semibold text-gray-900">{{ position.title }}</h3>
              </div>
              <p class="mt-4 text-gray-600">{{ position.description }}</p>
              <ul class="mt-6 space-y-3">
                <li v-for="requirement in position.requirements" :key="requirement" class="flex gap-x-3">
                  <CheckCircleIcon class="h-6 w-5 flex-none text-indigo-600" aria-hidden="true" />
                  <span class="text-sm text-gray-600">{{ requirement }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Email Application Section -->
        <div class="mt-12 text-center bg-white rounded-2xl shadow-lg p-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">投递简历</h3>
          <p class="text-gray-600 mb-4">如果您对我们的职位感兴趣，欢迎投递简历</p>
          <div class="inline-flex items-center justify-center space-x-2">
            <EnvelopeIcon class="h-5 w-5 text-indigo-600" />
            <a href="mailto:<EMAIL>" class="text-indigo-600 hover:text-indigo-700 font-medium">
              <EMAIL>
            </a>
          </div>
        </div>
      </div>

      <!-- Company Benefits -->
      <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <h3 class="text-2xl font-bold tracking-tight text-gray-900 mb-8">企业福利</h3>
        <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
          <div v-for="benefit in benefits" :key="benefit.name" class="flex flex-col">
            <dt class="text-base font-semibold leading-7 text-gray-900">
              <div class="mb-6 flex h-10 w-10 items-center justify-center rounded-lg bg-indigo-600">
                <component :is="benefit.icon" class="h-6 w-6 text-white" aria-hidden="true" />
              </div>
              {{ benefit.name }}
            </dt>
            <dd class="mt-1 flex flex-auto flex-col text-base leading-7 text-gray-600">
              <p class="flex-auto">{{ benefit.description }}</p>
            </dd>
          </div>
        </dl>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  CodeBracketIcon, 
  ChartBarIcon, 
  AcademicCapIcon,
  HeartIcon,
  RocketLaunchIcon,
  CheckCircleIcon,
  EnvelopeIcon
} from '@heroicons/vue/24/outline'
import PageHeader from '@/components/PageHeader.vue'

const positions = [
  {
    title: '大模型算法工程师',
    description: '负责AI大模型及相关应用的研发工作，推动产品技术创新。',
    icon: CodeBracketIcon,
    requirements: [
      '计算机相关专业或stem专业本科及以上学历',
      '对全栈开发工作有兴趣',
      '熟悉主流大模型框架和应用范式',
      '有相关项目或工作经验优先'
    ]
  },
  {
    title: '销售工程师',
    description: '负责AI产品的推广与销售，助力商业化模式展开。',
    icon: ChartBarIcon,
    requirements: [
      '本科及以上学历',
      '对前沿科技尤其是大模型应用产品有学习兴趣',
      '能独立完成客户拓展和产品交付，提供必要的需求支持',
      '有科技领域销售工作经验优先'
    ]
  }
]

const benefits = [
  {
    name: '技术成长',
    description: '提供丰富的培训资源和学习机会，支持员工职业发展。',
    icon: AcademicCapIcon,
  },
  {
    name: '优质薪酬',
    description: '具有竞争力的薪资待遇，完善的五险一金，节假日礼品及定期体检。',
    icon: HeartIcon,
  },
  {
    name: '发展机遇',
    description: '扁平化管理模式，开放的晋升通道，让每个人都有施展才华的机会。',
    icon: RocketLaunchIcon,
  }
]
</script>

<style scoped>
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
</style> 